import request from '@/utils/request';

export function sendFriendRequest(data) {
  return request({
    url: '/chat/apply/sendFriendApply',
    method: 'post',
    data: data,
  });
}

export function sendGroupRequest(data) {
  return request({
    url: '/chat/apply/sendGroupApply',
    method: 'post',
    data: data,
  });
}

export function getFriendRequest(params) {
  return request({
    url: '/chat/apply/getFriendRequest',
    method: 'get',
    params: params,
  });
}

export function acceptFriendRequest(data) {
  return request({
    url: '/chat/apply/acceptFriendRequest',
    method: 'post',
    data: data,
  });
}

export function rejectFriendRequest(data) {
  return request({
    url: '/chat/apply/rejectFriendRequest',
    method: 'post',
    data: data,
  });
}

export function acceptGroupRequest(data) {
  return request({
    url: '/chat/apply/acceptGroupRequest',
    method: 'post',
    data: data,
  });
}

export function rejectGroupRequest(data) {
  return request({
    url: '/chat/apply/rejectGroupRequest',
    method: 'post',
    data: data,
  });
}

export function createGroup(data) {
  return request({
    url: '/group/createGroup',
    method: 'post',
    data: data,
  });
}

export function handleGroupRequest(data) {
  return request({
    url: '/chat/apply/handleGroupRequest',
    method: 'post',
    data: data,
  });
}

/**
 * 根据名称搜索群组
 * @param {Object} params - 包含群组名称的参数对象
 * @returns {Promise} 返回Promise对象
 */
export function searchGroupByName(params) {
  return request({
    url: '/chat/apply/searchGroupByName',
    method: 'get',
    params: { groupName: params },
  });
}

export function updateGroupName(oldName, newName) {
  return request({
    url: '/group/updateGroupName',
    method: 'get',
    params: { oldName: oldName, newName: newName },
  });
}
