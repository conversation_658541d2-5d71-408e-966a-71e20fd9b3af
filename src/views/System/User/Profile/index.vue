<template>
  <div class="user-center" :class="{ 'dark-theme': settings.theme === 'dark' }">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-content">
        <div class="nav-left">
          <h2>
            <i class="el-icon-user-solid"></i>
            个人中心
          </h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>用户管理</el-breadcrumb-item>
            <el-breadcrumb-item>个人中心</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="nav-right">
          <el-badge :value="unreadCount" class="notification-badge">
            <el-button type="text" icon="el-icon-bell" @click="showNotifications = true">
              通知
            </el-button>
          </el-badge>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-refresh"
            :loading="loading"
            @click="refreshData"
          >
            刷新数据
          </el-button>
          <el-dropdown @command="handleCommand">
            <el-button size="small" icon="el-icon-more">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="export">导出数据</el-dropdown-item>
              <el-dropdown-item command="backup">备份资料</el-dropdown-item>
              <el-dropdown-item command="print">打印页面</el-dropdown-item>
              <el-dropdown-item command="help" divided>帮助中心</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <div class="main-container">
      <!-- 左侧菜单 -->
      <div class="sidebar-menu">
        <div class="user-avatar-section">
          <div class="avatar-wrapper">
            <el-avatar :size="60" :src="userInfo.avatar" icon="el-icon-user-solid"> </el-avatar>
            <div class="online-status" :class="{ online: userInfo.isOnline }"></div>
          </div>
          <div class="user-info">
            <h3>{{ userInfo.username }}</h3>
            <p class="user-role">
              <el-tag :type="getUserRoleType(userInfo.role)" size="mini">
                {{ userInfo.role }}
              </el-tag>
            </p>
            <p class="last-login">
              <i class="el-icon-time"></i>
              最后登录：{{ formatTime(userInfo.lastLogin) }}
            </p>
          </div>
        </div>

        <el-menu
          :default-active="activeSection"
          mode="vertical"
          class="sidebar-nav"
          @select="handleMenuSelect"
        >
          <el-menu-item index="dashboard">
            <i class="el-icon-data-analysis"></i>
            <span>数据总览</span>
          </el-menu-item>
          <el-menu-item index="profile">
            <i class="el-icon-user"></i>
            <span>基本信息</span>
          </el-menu-item>
          <el-menu-item index="security">
            <i class="el-icon-lock"></i>
            <span>账号安全</span>
          </el-menu-item>
          <el-menu-item index="api">
            <i class="el-icon-key"></i>
            <span>API管理</span>
          </el-menu-item>
          <el-menu-item index="activities">
            <i class="el-icon-time"></i>
            <span>活动记录</span>
          </el-menu-item>
          <el-menu-item index="messages">
            <i class="el-icon-message"></i>
            <span>消息中心</span>
            <el-badge v-if="unreadCount > 0" :value="unreadCount" class="menu-badge"></el-badge>
          </el-menu-item>
          <el-menu-item index="content">
            <i class="el-icon-document"></i>
            <span>内容管理</span>
          </el-menu-item>
          <el-menu-item index="analytics">
            <i class="el-icon-pie-chart"></i>
            <span>数据分析</span>
          </el-menu-item>
          <el-menu-item index="billing">
            <i class="el-icon-wallet"></i>
            <span>账单中心</span>
          </el-menu-item>
          <el-menu-item index="settings">
            <i class="el-icon-setting"></i>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容区 -->
      <div class="main-content">
        <!-- 数据总览 -->
        <div v-show="activeSection === 'dashboard'" class="content-section">
          <div class="section-header">
            <h3>数据总览</h3>
            <div class="header-actions">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                @change="updateCharts"
              >
              </el-date-picker>
            </div>
          </div>

          <!-- 统计卡片 -->
          <div class="stats-overview">
            <div v-for="(stat, index) in statsData" :key="index" class="stat-card">
              <div class="stat-icon" :class="stat.iconClass">
                <i :class="stat.icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(stat.value) }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div
                  class="stat-trend"
                  :class="{ positive: stat.trend > 0, negative: stat.trend < 0 }"
                >
                  <i :class="stat.trend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(stat.trend) }}%
                </div>
              </div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="charts-container">
            <div class="chart-item">
              <div class="chart-header">
                <h4>访问趋势</h4>
                <el-button-group size="mini">
                  <el-button
                    :type="chartTimeRange === '7d' ? 'primary' : ''"
                    @click="setChartTimeRange('7d')"
                    >7天</el-button
                  >
                  <el-button
                    :type="chartTimeRange === '30d' ? 'primary' : ''"
                    @click="setChartTimeRange('30d')"
                    >30天</el-button
                  >
                  <el-button
                    :type="chartTimeRange === '90d' ? 'primary' : ''"
                    @click="setChartTimeRange('90d')"
                    >90天</el-button
                  >
                </el-button-group>
              </div>
              <div ref="visitChart" class="chart-canvas"></div>
            </div>

            <div class="chart-item">
              <div class="chart-header">
                <h4>内容分布</h4>
                <el-dropdown @command="handleChartExport">
                  <el-button type="text" icon="el-icon-more"></el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="png">导出为PNG</el-dropdown-item>
                    <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div ref="contentChart" class="chart-canvas"></div>
            </div>

            <div class="chart-item full-width">
              <div class="chart-header">
                <h4>用户活跃度热力图</h4>
              </div>
              <div ref="heatmapChart" class="chart-canvas"></div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions-section">
            <h4>快速操作</h4>
            <div class="quick-actions">
              <div class="action-item" @click="createContent">
                <i class="el-icon-edit-outline"></i>
                <span>创建内容</span>
              </div>
              <div class="action-item" @click="uploadFile">
                <i class="el-icon-upload"></i>
                <span>上传文件</span>
              </div>
              <div class="action-item" @click="viewAnalytics">
                <i class="el-icon-data-line"></i>
                <span>查看分析</span>
              </div>
              <div class="action-item" @click="manageAPI">
                <i class="el-icon-setting"></i>
                <span>API管理</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息 -->
        <div v-show="activeSection === 'profile'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>基本信息</span>
              <el-button type="primary" size="small" icon="el-icon-edit" @click="editProfile"
                >编辑资料</el-button
              >
            </div>

            <div class="profile-content">
              <div class="avatar-section">
                <div class="avatar-wrapper">
                  <el-avatar
                    :size="120"
                    :src="userInfo.avatar"
                    icon="el-icon-user-solid"
                  ></el-avatar>
                  <el-upload
                    class="avatar-upload"
                    action="#"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload"
                    accept="image/*"
                  >
                    <el-button size="mini" type="primary" icon="el-icon-camera">更换头像</el-button>
                  </el-upload>
                </div>
                <div class="verification-badges">
                  <el-tag v-if="userInfo.emailVerified" type="success" size="mini">
                    <i class="el-icon-success"></i> 邮箱已验证
                  </el-tag>
                  <el-tag v-if="userInfo.phoneVerified" type="success" size="mini">
                    <i class="el-icon-success"></i> 手机已验证
                  </el-tag>
                  <el-tag v-if="userInfo.twoFactorEnabled" type="warning" size="mini">
                    <i class="el-icon-lock"></i> 双重验证
                  </el-tag>
                </div>
              </div>

              <div class="info-section">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <label>用户名：</label>
                      <span>{{ userInfo.username }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>昵称：</label>
                      <span>{{ userInfo.nickname }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>邮箱：</label>
                      <span>{{ userInfo.email }}</span>
                      <el-tag v-if="!userInfo.emailVerified" type="warning" size="mini"
                        >待验证</el-tag
                      >
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>手机号：</label>
                      <span>{{ userInfo.phone || '未绑定' }}</span>
                      <el-tag
                        v-if="userInfo.phone && !userInfo.phoneVerified"
                        type="warning"
                        size="mini"
                        >待验证</el-tag
                      >
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>注册时间：</label>
                      <span>{{ formatTime(userInfo.createTime) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>最后登录：</label>
                      <span>{{ formatTime(userInfo.lastLogin) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="info-item">
                      <label>个人简介：</label>
                      <span>{{ userInfo.bio || '这个人很懒，什么都没留下...' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 账户统计 -->
            <div class="account-stats">
              <h4>账户统计</h4>
              <el-row :gutter="20">
                <el-col v-for="(stat, index) in accountStats" :key="index" :span="6">
                  <div class="mini-stat">
                    <div class="stat-number">{{ formatNumber(stat.value) }}</div>
                    <div class="stat-desc">{{ stat.label }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>

        <!-- 账号安全 -->
        <div v-show="activeSection === 'security'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>账号安全</span>
              <div class="security-level">
                <span>安全等级：</span>
                <el-progress
                  :percentage="securityLevel"
                  :color="getSecurityColor(securityLevel)"
                  :show-text="false"
                  :stroke-width="8"
                  style="width: 100px; margin-right: 10px"
                ></el-progress>
                <el-tag :type="getSecurityTagType(securityLevel)" size="mini">
                  {{ getSecurityLevelText(securityLevel) }}
                </el-tag>
              </div>
            </div>

            <div class="security-items">
              <div class="security-item">
                <div class="item-info">
                  <div class="item-title">
                    <i class="el-icon-lock"></i>
                    登录密码
                  </div>
                  <div class="item-desc">定期更换密码，提高账户安全性</div>
                  <div class="item-status">
                    最后修改：{{ formatTime(userInfo.passwordUpdateTime) }}
                  </div>
                </div>
                <el-button type="primary" size="small" @click="showPasswordDialog = true"
                  >修改密码</el-button
                >
              </div>

              <div class="security-item">
                <div class="item-info">
                  <div class="item-title">
                    <i class="el-icon-mobile-phone"></i>
                    手机号码
                  </div>
                  <div class="item-desc">用于接收验证码和重要通知</div>
                  <div class="item-status" :class="{ verified: userInfo.phoneVerified }">
                    {{ userInfo.phone ? (userInfo.phoneVerified ? '已验证' : '待验证') : '未绑定' }}
                  </div>
                </div>
                <el-button type="primary" size="small" @click="showPhoneDialog = true">
                  {{ userInfo.phone ? '修改' : '绑定' }}手机
                </el-button>
              </div>

              <div class="security-item">
                <div class="item-info">
                  <div class="item-title">
                    <i class="el-icon-message"></i>
                    邮箱地址
                  </div>
                  <div class="item-desc">用于找回密码和接收通知邮件</div>
                  <div class="item-status" :class="{ verified: userInfo.emailVerified }">
                    {{ userInfo.emailVerified ? '已验证' : '待验证' }}
                  </div>
                </div>
                <el-button type="primary" size="small" @click="showEmailDialog = true">
                  {{ userInfo.emailVerified ? '修改' : '验证' }}邮箱
                </el-button>
              </div>

              <div class="security-item">
                <div class="item-info">
                  <div class="item-title">
                    <i class="el-icon-key"></i>
                    双重验证
                  </div>
                  <div class="item-desc">使用验证器应用增强账户安全</div>
                  <div class="item-status" :class="{ enabled: userInfo.twoFactorEnabled }">
                    {{ userInfo.twoFactorEnabled ? '已启用' : '未启用' }}
                  </div>
                </div>
                <el-switch
                  v-model="userInfo.twoFactorEnabled"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @change="toggleTwoFactor"
                >
                </el-switch>
              </div>

              <div class="security-item">
                <div class="item-info">
                  <div class="item-title">
                    <i class="el-icon-view"></i>
                    登录设备
                  </div>
                  <div class="item-desc">管理已登录的设备和会话</div>
                  <div class="item-status">当前在线设备：{{ loginDevices.length }}个</div>
                </div>
                <el-button type="primary" size="small" @click="showDevicesDialog = true"
                  >管理设备</el-button
                >
              </div>
            </div>

            <!-- 安全日志 -->
            <div class="security-logs">
              <h4>最近安全活动</h4>
              <div class="log-list">
                <div v-for="log in securityLogs.slice(0, 5)" :key="log.id" class="log-item">
                  <div class="log-icon" :class="getLogTypeClass(log.type)">
                    <i :class="getLogIcon(log.type)"></i>
                  </div>
                  <div class="log-content">
                    <div class="log-title">{{ log.title }}</div>
                    <div class="log-desc">{{ log.description }}</div>
                    <div class="log-time">{{ formatRelativeTime(log.time) }}</div>
                  </div>
                </div>
              </div>
              <el-button type="text" @click="viewAllLogs">查看所有安全日志</el-button>
            </div>
          </el-card>
        </div>

        <!-- API管理 -->
        <div v-show="activeSection === 'api'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>API密钥管理</span>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="showCreateApiDialog = true"
              >
                创建新密钥
              </el-button>
            </div>

            <div v-if="apiKeys.length > 0" class="api-usage-overview">
              <h4>API使用概览</h4>
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="usage-stat">
                    <div class="stat-value">{{ formatNumber(apiUsage.totalCalls) }}</div>
                    <div class="stat-label">总调用次数</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="usage-stat">
                    <div class="stat-value">{{ formatNumber(apiUsage.todayCalls) }}</div>
                    <div class="stat-label">今日调用</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="usage-stat">
                    <div class="stat-value">{{ apiUsage.successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="usage-stat">
                    <div class="stat-value">{{ apiUsage.avgResponseTime }}ms</div>
                    <div class="stat-label">平均响应时间</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div v-if="apiKeys.length > 0" class="api-list">
              <div v-for="apiKey in apiKeys" :key="apiKey.id" class="api-item">
                <div class="api-header">
                  <h4>{{ apiKey.name }}</h4>
                  <div class="api-status">
                    <el-tag :type="apiKey.status === 'active' ? 'success' : 'danger'" size="small">
                      {{ apiKey.status === 'active' ? '活跃' : '已禁用' }}
                    </el-tag>
                  </div>
                </div>

                <div class="api-key">
                  <code>{{ showFullKey[apiKey.id] ? apiKey.key : maskApiKey(apiKey.key) }}</code>
                  <el-button
                    type="text"
                    icon="el-icon-view"
                    :title="showFullKey[apiKey.id] ? '隐藏' : '显示'"
                    @click="toggleKeyVisibility(apiKey.id)"
                  ></el-button>
                  <el-button
                    type="text"
                    icon="el-icon-copy-document"
                    title="复制"
                    @click="copyApiKey(apiKey.key)"
                  ></el-button>
                </div>

                <div class="api-info">
                  <div class="info-item">
                    <span>创建时间：{{ formatTime(apiKey.createTime) }}</span>
                  </div>
                  <div class="info-item">
                    <span>最后使用：{{ formatTime(apiKey.lastUsed) }}</span>
                  </div>
                  <div class="info-item">
                    <span>调用次数：{{ formatNumber(apiKey.callCount) }}</span>
                  </div>
                  <div class="info-item">
                    <span>权限范围：{{ apiKey.permissions.join(', ') }}</span>
                  </div>
                </div>

                <div class="api-actions">
                  <el-button size="mini" icon="el-icon-edit" @click="editApiKey(apiKey)"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    :type="apiKey.status === 'active' ? 'warning' : 'success'"
                    :icon="apiKey.status === 'active' ? 'el-icon-close' : 'el-icon-check'"
                    @click="toggleApiStatus(apiKey.id)"
                  >
                    {{ apiKey.status === 'active' ? '禁用' : '启用' }}
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="deleteApiKey(apiKey.id)"
                    >删除</el-button
                  >
                  <el-button
                    size="mini"
                    type="info"
                    icon="el-icon-data-line"
                    @click="viewApiStats(apiKey)"
                    >统计</el-button
                  >
                </div>
              </div>
            </div>

            <div v-else class="empty-state">
              <i class="el-icon-key"></i>
              <p>您还没有创建任何API密钥</p>
              <el-button type="primary" @click="showCreateApiDialog = true">立即创建</el-button>
            </div>
          </el-card>
        </div>

        <!-- 活动记录 -->
        <div v-show="activeSection === 'activities'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>活动记录</span>
              <div class="filters">
                <el-select
                  v-model="activityFilter"
                  placeholder="筛选类型"
                  size="small"
                  @change="filterActivities"
                >
                  <el-option label="全部" value="all"></el-option>
                  <el-option label="登录" value="login"></el-option>
                  <el-option label="安全" value="security"></el-option>
                  <el-option label="API" value="api"></el-option>
                  <el-option label="内容" value="content"></el-option>
                </el-select>
                <el-date-picker
                  v-model="activityDateRange"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="filterActivities"
                >
                </el-date-picker>
              </div>
            </div>

            <div class="activity-timeline">
              <div v-for="activity in filteredActivities" :key="activity.id" class="timeline-item">
                <div class="timeline-dot" :class="getActivityTypeClass(activity.type)"></div>
                <div class="timeline-content">
                  <div class="activity-header">
                    <h4>{{ activity.title }}</h4>
                    <span class="activity-time">{{ formatRelativeTime(activity.time) }}</span>
                  </div>
                  <p>{{ activity.description }}</p>
                  <div
                    v-if="activity.location || activity.device || activity.ip"
                    class="activity-meta"
                  >
                    <span v-if="activity.location">
                      <i class="el-icon-location-outline"></i>
                      {{ activity.location }}
                    </span>
                    <span v-if="activity.device">
                      <i class="el-icon-mobile-phone"></i>
                      {{ activity.device }}
                    </span>
                    <span v-if="activity.ip">
                      <i class="el-icon-connection"></i>
                      {{ activity.ip }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="hasMoreActivities" class="load-more">
              <el-button :loading="loadingActivities" @click="loadMoreActivities"
                >加载更多</el-button
              >
            </div>
          </el-card>
        </div>

        <!-- 消息中心 -->
        <div v-show="activeSection === 'messages'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>消息中心</span>
              <div class="message-actions">
                <el-button size="small" icon="el-icon-check" @click="markAllAsRead"
                  >全部已读</el-button
                >
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  type="danger"
                  @click="clearAllMessages"
                  >清空消息</el-button
                >
              </div>
            </div>

            <el-tabs v-model="activeMessageTab">
              <el-tab-pane label="系统通知" name="system">
                <template #label>
                  <span>
                    系统通知
                    <el-badge
                      v-if="unreadSystemCount > 0"
                      :value="unreadSystemCount"
                      class="tab-badge"
                    ></el-badge>
                  </span>
                </template>

                <div class="message-list">
                  <div
                    v-for="message in systemMessages"
                    :key="message.id"
                    class="message-item"
                    :class="{ unread: !message.read }"
                  >
                    <div class="message-icon" :class="getMessageTypeClass(message.type)">
                      <i :class="getMessageIcon(message.type)"></i>
                    </div>
                    <div class="message-content">
                      <h4>{{ message.title }}</h4>
                      <p>{{ message.content }}</p>
                      <div class="message-time">{{ formatRelativeTime(message.time) }}</div>
                    </div>
                    <div class="message-actions">
                      <el-button
                        v-if="!message.read"
                        type="text"
                        size="mini"
                        @click="markAsRead(message.id, 'system')"
                      >
                        标记已读
                      </el-button>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="deleteMessage(message.id, 'system')"
                      >
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="互动消息" name="interaction">
                <template #label>
                  <span>
                    互动消息
                    <el-badge
                      v-if="unreadInteractionCount > 0"
                      :value="unreadInteractionCount"
                      class="tab-badge"
                    ></el-badge>
                  </span>
                </template>

                <div class="message-list">
                  <div
                    v-for="message in interactionMessages"
                    :key="message.id"
                    class="message-item"
                    :class="{ unread: !message.read }"
                  >
                    <div class="user-avatar">
                      <el-avatar
                        :size="32"
                        :src="message.avatar"
                        icon="el-icon-user-solid"
                      ></el-avatar>
                    </div>
                    <div class="message-content">
                      <h4>{{ message.title }}</h4>
                      <p>{{ message.content }}</p>
                      <div class="message-time">{{ formatRelativeTime(message.time) }}</div>
                    </div>
                    <div class="message-actions">
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-chat-dot-square"
                        @click="replyMessage(message)"
                      >
                        回复
                      </el-button>
                      <el-button
                        v-if="!message.read"
                        type="text"
                        size="mini"
                        @click="markAsRead(message.id, 'interaction')"
                      >
                        标记已读
                      </el-button>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        @click="deleteMessage(message.id, 'interaction')"
                      >
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="公告通知" name="announcement">
                <div class="announcement-list">
                  <div
                    v-for="announcement in announcements"
                    :key="announcement.id"
                    class="announcement-item"
                  >
                    <div class="announcement-header">
                      <h3>{{ announcement.title }}</h3>
                      <el-tag :type="announcement.priority" size="mini">
                        {{ announcement.priorityText }}
                      </el-tag>
                    </div>
                    <div class="announcement-content" v-html="announcement.content"></div>
                    <div class="announcement-footer">
                      <span>发布时间：{{ formatTime(announcement.publishTime) }}</span>
                      <span>发布人：{{ announcement.publisher }}</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </div>

        <!-- 内容管理 -->
        <div v-show="activeSection === 'content'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>内容管理</span>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="showCreateContentDialog = true"
              >
                新建内容
              </el-button>
            </div>

            <div class="content-toolbar">
              <div class="toolbar-left">
                <el-input
                  v-model="contentSearchKeyword"
                  placeholder="搜索内容..."
                  size="small"
                  suffix-icon="el-icon-search"
                  style="width: 200px"
                  @input="searchContent"
                >
                </el-input>
                <el-select v-model="contentStatusFilter" size="small" @change="filterContent">
                  <el-option label="全部状态" value="all"></el-option>
                  <el-option label="已发布" value="published"></el-option>
                  <el-option label="草稿" value="draft"></el-option>
                  <el-option label="审核中" value="pending"></el-option>
                </el-select>
                <el-select v-model="contentTypeFilter" size="small" @change="filterContent">
                  <el-option label="全部类型" value="all"></el-option>
                  <el-option label="文章" value="article"></el-option>
                  <el-option label="视频" value="video"></el-option>
                  <el-option label="图片" value="image"></el-option>
                </el-select>
              </div>
              <div class="toolbar-right">
                <el-button
                  size="small"
                  :disabled="selectedContent.length === 0"
                  @click="batchDelete"
                >
                  批量删除
                </el-button>
                <el-button size="small" @click="exportContent">导出内容</el-button>
              </div>
            </div>

            <el-table
              v-loading="loadingContent"
              :data="filteredContent"
              @selection-change="handleContentSelection"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="title" label="标题" min-width="200">
                <template slot-scope="scope">
                  <div class="content-title">
                    <span>{{ scope.row.title }}</span>
                    <el-tag v-if="scope.row.featured" type="warning" size="mini">置顶</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="80">
                <template slot-scope="scope">
                  <el-tag :type="getContentTypeTag(scope.row.type)" size="mini">
                    {{ getContentTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="getContentStatusTag(scope.row.status)" size="mini">
                    {{ getContentStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="views" label="浏览量" width="80">
                <template slot-scope="scope">
                  {{ formatNumber(scope.row.views) }}
                </template>
              </el-table-column>
              <el-table-column prop="likes" label="点赞" width="70">
                <template slot-scope="scope">
                  {{ formatNumber(scope.row.likes) }}
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="160">
                <template slot-scope="scope">
                  {{ formatTime(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="viewContent(scope.row)"
                    >查看</el-button
                  >
                  <el-button type="text" size="small" @click="editContent(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    type="text"
                    size="small"
                    :class="{
                      'text-success': scope.row.status === 'draft',
                      'text-warning': scope.row.status === 'published',
                    }"
                    @click="toggleContentStatus(scope.row)"
                  >
                    {{ scope.row.status === 'published' ? '下线' : '发布' }}
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="text-danger"
                    @click="deleteContent(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-wrapper">
              <el-pagination
                :current-page="contentPagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="contentPagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="contentPagination.total"
                @size-change="handleContentSizeChange"
                @current-change="handleContentCurrentChange"
              >
              </el-pagination>
            </div>
          </el-card>
        </div>

        <!-- 数据分析 -->
        <div v-show="activeSection === 'analytics'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>数据分析</span>
              <div class="analysis-controls">
                <el-date-picker
                  v-model="analysisDateRange"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="updateAnalysisData"
                >
                </el-date-picker>
                <el-button size="small" icon="el-icon-document" @click="generateReport"
                  >生成报告</el-button
                >
              </div>
            </div>

            <!-- 关键指标 -->
            <div class="analysis-metrics">
              <el-row :gutter="20">
                <el-col v-for="metric in analysisMetrics" :key="metric.key" :span="6">
                  <div class="metric-card">
                    <div class="metric-icon" :class="metric.iconClass">
                      <i :class="metric.icon"></i>
                    </div>
                    <div class="metric-content">
                      <div class="metric-value">{{ formatNumber(metric.value) }}</div>
                      <div class="metric-label">{{ metric.label }}</div>
                      <div
                        class="metric-change"
                        :class="{ positive: metric.change > 0, negative: metric.change < 0 }"
                      >
                        <i
                          :class="metric.change > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                        ></i>
                        {{ Math.abs(metric.change) }}%
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 详细图表 -->
            <div class="analysis-charts">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="chart-container">
                    <h4>用户增长趋势</h4>
                    <div ref="userGrowthChart" class="chart-canvas" style="height: 300px"></div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="chart-container">
                    <h4>内容互动分析</h4>
                    <div ref="interactionChart" class="chart-canvas" style="height: 300px"></div>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div class="chart-container">
                    <h4>流量来源分析</h4>
                    <div ref="trafficSourceChart" class="chart-canvas" style="height: 400px"></div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 数据表格 -->
            <div class="analysis-tables">
              <h4>详细数据</h4>
              <el-tabs v-model="activeAnalysisTab">
                <el-tab-pane label="热门内容" name="popular">
                  <el-table :data="popularContent">
                    <el-table-column prop="title" label="标题"></el-table-column>
                    <el-table-column prop="views" label="浏览量" width="100">
                      <template slot-scope="scope">
                        {{ formatNumber(scope.row.views) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="likes" label="点赞数" width="100">
                      <template slot-scope="scope">
                        {{ formatNumber(scope.row.likes) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="comments" label="评论数" width="100">
                      <template slot-scope="scope">
                        {{ formatNumber(scope.row.comments) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="shares" label="分享数" width="100">
                      <template slot-scope="scope">
                        {{ formatNumber(scope.row.shares) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>

                <el-tab-pane label="用户行为" name="behavior">
                  <el-table :data="userBehaviorData">
                    <el-table-column prop="action" label="行为类型"></el-table-column>
                    <el-table-column prop="count" label="次数" width="100">
                      <template slot-scope="scope">
                        {{ formatNumber(scope.row.count) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="percentage" label="占比" width="100">
                      <template slot-scope="scope"> {{ scope.row.percentage }}% </template>
                    </el-table-column>
                    <el-table-column prop="avgDuration" label="平均时长" width="120">
                      <template slot-scope="scope">
                        {{ formatDuration(scope.row.avgDuration) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </div>

        <!-- 账单中心 -->
        <div v-show="activeSection === 'billing'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>账单中心</span>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-wallet"
                @click="showPaymentDialog = true"
              >
                立即充值
              </el-button>
            </div>

            <!-- 账户余额 -->
            <div class="balance-overview">
              <div class="balance-card main-balance">
                <div class="balance-header">
                  <h3>账户余额</h3>
                  <el-button type="text" icon="el-icon-refresh" @click="refreshBalance"
                    >刷新</el-button
                  >
                </div>
                <div class="balance-amount">
                  <span class="currency">¥</span>
                  <span class="amount">{{ formatNumber(accountBalance.available, 2) }}</span>
                </div>
                <div class="balance-actions">
                  <el-button type="primary" size="small" @click="showPaymentDialog = true"
                    >充值</el-button
                  >
                  <el-button size="small" @click="showWithdrawDialog = true">提现</el-button>
                </div>
              </div>

              <div class="balance-card">
                <h4>冻结金额</h4>
                <div class="balance-amount">
                  <span class="currency">¥</span>
                  <span class="amount">{{ formatNumber(accountBalance.frozen, 2) }}</span>
                </div>
              </div>

              <div class="balance-card">
                <h4>累计消费</h4>
                <div class="balance-amount">
                  <span class="currency">¥</span>
                  <span class="amount">{{ formatNumber(accountBalance.totalSpent, 2) }}</span>
                </div>
              </div>

              <div class="balance-card">
                <h4>累计收入</h4>
                <div class="balance-amount">
                  <span class="currency">¥</span>
                  <span class="amount">{{ formatNumber(accountBalance.totalEarned, 2) }}</span>
                </div>
              </div>
            </div>

            <!-- 账单记录 -->
            <div class="billing-history">
              <div class="history-header">
                <h4>账单记录</h4>
                <div class="history-filters">
                  <el-select v-model="billTypeFilter" size="small" @change="filterBills">
                    <el-option label="全部类型" value="all"></el-option>
                    <el-option label="充值" value="recharge"></el-option>
                    <el-option label="消费" value="consume"></el-option>
                    <el-option label="提现" value="withdraw"></el-option>
                    <el-option label="退款" value="refund"></el-option>
                  </el-select>
                  <el-date-picker
                    v-model="billDateRange"
                    type="daterange"
                    size="small"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="filterBills"
                  >
                  </el-date-picker>
                </div>
              </div>

              <el-table v-loading="loadingBills" :data="filteredBills">
                <el-table-column prop="id" label="订单号" width="150"></el-table-column>
                <el-table-column prop="type" label="类型" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="getBillTypeTag(scope.row.type)" size="mini">
                      {{ getBillTypeText(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="金额" width="120">
                  <template slot-scope="scope">
                    <span
                      :class="{
                        'text-success': scope.row.amount > 0,
                        'text-danger': scope.row.amount < 0,
                      }"
                    >
                      {{ scope.row.amount > 0 ? '+' : '' }}¥{{
                        formatNumber(Math.abs(scope.row.amount), 2)
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="getBillStatusTag(scope.row.status)" size="mini">
                      {{ getBillStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                  <template slot-scope="scope">
                    {{ formatTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="viewBillDetail(scope.row)"
                      >详情</el-button
                    >
                    <el-button
                      v-if="scope.row.status === 'success'"
                      type="text"
                      size="small"
                      @click="downloadReceipt(scope.row)"
                    >
                      发票
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination-wrapper">
                <el-pagination
                  :current-page="billPagination.currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="billPagination.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="billPagination.total"
                  @size-change="handleBillSizeChange"
                  @current-change="handleBillCurrentChange"
                >
                </el-pagination>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 系统设置 -->
        <div v-show="activeSection === 'settings'" class="content-section">
          <el-card shadow="never">
            <div slot="header" class="card-header">
              <span>系统设置</span>
              <div class="settings-actions">
                <el-button size="small" @click="resetSettings">重置默认</el-button>
                <el-button type="primary" size="small" @click="saveSettings">保存设置</el-button>
              </div>
            </div>

            <el-tabs v-model="activeSettingsTab" tab-position="left">
              <el-tab-pane label="界面设置" name="appearance">
                <div class="settings-section">
                  <h4>主题设置</h4>
                  <el-radio-group v-model="settings.theme">
                    <el-radio label="light">浅色主题</el-radio>
                    <el-radio label="dark">深色主题</el-radio>
                    <el-radio label="auto">跟随系统</el-radio>
                  </el-radio-group>
                </div>

                <div class="settings-section">
                  <h4>语言设置</h4>
                  <el-select v-model="settings.language">
                    <el-option label="简体中文" value="zh-CN"></el-option>
                    <el-option label="English" value="en-US"></el-option>
                    <el-option label="日本語" value="ja-JP"></el-option>
                  </el-select>
                </div>

                <div class="settings-section">
                  <h4>界面设置</h4>
                  <div class="setting-item">
                    <span>紧凑模式</span>
                    <el-switch v-model="settings.compactMode"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>显示侧边栏</span>
                    <el-switch v-model="settings.showSidebar"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>显示面包屑</span>
                    <el-switch v-model="settings.showBreadcrumb"></el-switch>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="通知设置" name="notifications">
                <div class="settings-section">
                  <h4>邮件通知</h4>
                  <div class="setting-item">
                    <span>系统通知</span>
                    <el-switch v-model="settings.emailNotification.system"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>安全提醒</span>
                    <el-switch v-model="settings.emailNotification.security"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>账单提醒</span>
                    <el-switch v-model="settings.emailNotification.billing"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>营销推广</span>
                    <el-switch v-model="settings.emailNotification.marketing"></el-switch>
                  </div>
                </div>

                <div class="settings-section">
                  <h4>短信通知</h4>
                  <div class="setting-item">
                    <span>登录验证</span>
                    <el-switch v-model="settings.smsNotification.login"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>安全警告</span>
                    <el-switch v-model="settings.smsNotification.security"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>重要通知</span>
                    <el-switch v-model="settings.smsNotification.important"></el-switch>
                  </div>
                </div>

                <div class="settings-section">
                  <h4>桌面通知</h4>
                  <div class="setting-item">
                    <span>浏览器推送</span>
                    <el-switch v-model="settings.desktopNotification"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>声音提醒</span>
                    <el-switch v-model="settings.soundNotification"></el-switch>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="隐私设置" name="privacy">
                <div class="settings-section">
                  <h4>个人信息</h4>
                  <div class="setting-item">
                    <span>公开个人资料</span>
                    <el-switch v-model="settings.privacy.publicProfile"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>显示在线状态</span>
                    <el-switch v-model="settings.privacy.showOnlineStatus"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>允许搜索</span>
                    <el-switch v-model="settings.privacy.allowSearch"></el-switch>
                  </div>
                </div>

                <div class="settings-section">
                  <h4>数据设置</h4>
                  <div class="setting-item">
                    <span>收集使用数据</span>
                    <el-switch v-model="settings.privacy.collectUsageData"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>个性化推荐</span>
                    <el-switch v-model="settings.privacy.personalizedRecommendations"></el-switch>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="高级设置" name="advanced">
                <div class="settings-section">
                  <h4>API设置</h4>
                  <div class="setting-item">
                    <span>API访问频率限制</span>
                    <el-input-number
                      v-model="settings.advanced.apiRateLimit"
                      :min="1"
                      :max="1000"
                    ></el-input-number>
                    <span class="setting-desc">次/分钟</span>
                  </div>
                  <div class="setting-item">
                    <span>API超时时间</span>
                    <el-input-number
                      v-model="settings.advanced.apiTimeout"
                      :min="1000"
                      :max="60000"
                      :step="1000"
                    ></el-input-number>
                    <span class="setting-desc">毫秒</span>
                  </div>
                </div>

                <div class="settings-section">
                  <h4>缓存设置</h4>
                  <div class="setting-item">
                    <span>启用本地缓存</span>
                    <el-switch v-model="settings.advanced.enableCache"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>缓存过期时间</span>
                    <el-input-number
                      v-model="settings.advanced.cacheExpiry"
                      :min="1"
                      :max="24"
                    ></el-input-number>
                    <span class="setting-desc">小时</span>
                  </div>
                  <div class="setting-item">
                    <span></span>
                    <el-button size="small" @click="clearCache">清理缓存</el-button>
                  </div>
                </div>

                <div class="settings-section">
                  <h4>实验性功能</h4>
                  <div class="setting-item">
                    <span>启用新版编辑器</span>
                    <el-switch v-model="settings.advanced.experimentalEditor"></el-switch>
                  </div>
                  <div class="setting-item">
                    <span>启用AI助手</span>
                    <el-switch v-model="settings.advanced.aiAssistant"></el-switch>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 各种弹窗组件 -->
    <!-- 修改密码弹窗 -->
    <el-dialog title="修改密码" :visible.sync="showPasswordDialog" width="400px">
      <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordChange">确定</el-button>
      </div>
    </el-dialog>

    <!-- 创建API密钥弹窗 -->
    <el-dialog title="创建API密钥" :visible.sync="showCreateApiDialog" width="500px">
      <el-form ref="apiForm" :model="apiForm" :rules="apiRules">
        <el-form-item label="密钥名称" prop="name">
          <el-input v-model="apiForm.name" placeholder="请输入密钥名称"></el-input>
        </el-form-item>
        <el-form-item label="权限范围" prop="permissions">
          <el-checkbox-group v-model="apiForm.permissions">
            <el-checkbox label="read">只读权限</el-checkbox>
            <el-checkbox label="write">写入权限</el-checkbox>
            <el-checkbox label="delete">删除权限</el-checkbox>
            <el-checkbox label="admin">管理权限</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="过期时间" prop="expiry">
          <el-select v-model="apiForm.expiry">
            <el-option label="30天" value="30"></el-option>
            <el-option label="90天" value="90"></el-option>
            <el-option label="180天" value="180"></el-option>
            <el-option label="1年" value="365"></el-option>
            <el-option label="永不过期" value="never"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="apiForm.description"
            type="textarea"
            placeholder="请输入API密钥的用途描述"
            rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showCreateApiDialog = false">取消</el-button>
        <el-button type="primary" @click="submitCreateApi">创建</el-button>
      </div>
    </el-dialog>

    <!-- 充值弹窗 -->
    <el-dialog title="账户充值" :visible.sync="showPaymentDialog" width="500px">
      <div class="payment-dialog">
        <div class="amount-section">
          <h4>充值金额</h4>
          <div class="amount-options">
            <div
              v-for="amount in rechargeAmounts"
              :key="amount"
              class="amount-option"
              :class="{ active: paymentForm.amount === amount }"
              @click="paymentForm.amount = amount"
            >
              ¥{{ amount }}
            </div>
          </div>
          <el-input
            v-model.number="paymentForm.amount"
            placeholder="或输入自定义金额"
            class="custom-amount"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </div>

        <div class="payment-method">
          <h4>支付方式</h4>
          <el-radio-group v-model="paymentForm.method">
            <el-radio label="alipay">
              <img src="/icons/alipay.png" class="payment-icon" alt="支付宝" /> 支付宝
            </el-radio>
            <el-radio label="wechat">
              <img src="/icons/wechat.png" class="payment-icon" alt="微信支付" /> 微信支付
            </el-radio>
            <el-radio label="bank">
              <img src="/icons/bank.png" class="payment-icon" alt="银行卡" /> 银行卡
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="showPaymentDialog = false">取消</el-button>
        <el-button type="primary" :loading="loadingPayment" @click="submitPayment">
          立即支付
        </el-button>
      </div>
    </el-dialog>

    <!-- 通知中心弹窗 -->
    <el-drawer title="通知中心" :visible.sync="showNotifications" direction="right" size="400px">
      <div class="notification-drawer">
        <div class="notification-header">
          <el-button type="text" @click="markAllAsRead">全部标记已读</el-button>
          <el-button type="text" @click="clearAllNotifications">清空通知</el-button>
        </div>

        <el-tabs v-model="activeNotificationTab">
          <el-tab-pane label="全部" name="all">
            <div class="notification-list">
              <div
                v-for="notification in allNotifications"
                :key="notification.id"
                class="notification-item-drawer"
                :class="{ unread: !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-content-drawer">
                  <h5>{{ notification.title }}</h5>
                  <p>{{ notification.content }}</p>
                  <span class="time">{{ formatRelativeTime(notification.time) }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="系统" name="system">
            <div class="notification-list">
              <div
                v-for="notification in systemNotifications"
                :key="notification.id"
                class="notification-item-drawer"
                :class="{ unread: !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-content-drawer">
                  <h5>{{ notification.title }}</h5>
                  <p>{{ notification.content }}</p>
                  <span class="time">{{ formatRelativeTime(notification.time) }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'UserCenter',

  data() {
    return {
      loading: false,
      activeSection: 'overview',

      // 用户信息
      userInfo: {
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        username: '张三',
        email: '<EMAIL>',
        phone: '138****8888',
        role: '高级用户',
        level: 'VIP5',
        isOnline: true,
        lastLogin: new Date('2024-01-15 14:30:00'),
        registerTime: new Date('2023-03-10 09:15:00'),
        loginCount: 1230,
        nickname: '张三同学',
        gender: '男',
        birthday: '1990-05-15',
        location: '北京市朝阳区',
        bio: '热爱技术，专注于前端开发领域',
        website: 'https://zhangsan.dev',
        company: '某科技有限公司',
        position: '前端工程师',
      },

      // 编辑表单
      editForm: {
        nickname: '',
        email: '',
        phone: '',
        gender: '',
        birthday: '',
        location: '',
        bio: '',
        website: '',
        company: '',
        position: '',
      },

      // 统计数据
      statsData: {
        totalViews: 125680,
        todayViews: 1268,
        totalLikes: 8945,
        todayLikes: 89,
        totalComments: 3456,
        todayComments: 34,
        followers: 12580,
      },

      // API密钥
      apiKeys: [
        {
          id: 1,
          name: '生产环境密钥',
          key: 'ak_live_***************',
          permissions: ['read', 'write'],
          status: 'active',
          createTime: new Date('2024-01-01'),
          lastUsed: new Date('2024-01-15'),
          usageCount: 1250,
        },
        {
          id: 2,
          name: '测试环境密钥',
          key: 'ak_test_***************',
          permissions: ['read'],
          status: 'inactive',
          createTime: new Date('2023-12-15'),
          lastUsed: new Date('2024-01-10'),
          usageCount: 567,
        },
      ],

      // 活动记录
      activityLogs: [
        {
          id: 1,
          action: '登录系统',
          ip: '*************',
          location: '北京',
          device: 'Chrome 120.0 / Windows 10',
          time: new Date('2024-01-15 14:30:00'),
          status: 'success',
        },
        {
          id: 2,
          action: '修改密码',
          ip: '*************',
          location: '北京',
          device: 'Chrome 120.0 / Windows 10',
          time: new Date('2024-01-15 10:20:00'),
          status: 'success',
        },
        {
          id: 3,
          action: '异常登录尝试',
          ip: '123.456.789.0',
          location: '上海',
          device: 'Unknown',
          time: new Date('2024-01-14 22:15:00'),
          status: 'failed',
        },
      ],

      // 系统消息
      systemMessages: [
        {
          id: 1,
          title: '系统维护通知',
          content: '系统将于今晚23:00-01:00进行维护，请提前保存数据',
          type: 'warning',
          read: false,
          time: new Date('2024-01-15 16:00:00'),
        },
        {
          id: 2,
          title: '新功能上线',
          content: 'AI助手功能已正式上线，快来体验吧！',
          type: 'info',
          read: true,
          time: new Date('2024-01-14 10:00:00'),
        },
      ],

      // 互动消息
      interactionMessages: [
        {
          id: 1,
          title: '新的评论',
          content: '用户"李四"评论了您的文章',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          read: false,
          time: new Date('2024-01-15 15:30:00'),
        },
        {
          id: 2,
          title: '新的关注者',
          content: '用户"王五"关注了您',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          read: false,
          time: new Date('2024-01-15 12:20:00'),
        },
      ],

      // 公告
      announcements: [
        {
          id: 1,
          title: '平台升级公告',
          content: '<p>尊敬的用户，我们将在本周末对平台进行重大升级...</p>',
          priority: 'danger',
          priorityText: '重要',
          publishTime: new Date('2024-01-15 09:00:00'),
          publisher: '系统管理员',
        },
      ],

      // 内容管理
      userContent: [
        {
          id: 1,
          title: 'Vue.js 3.0 深度解析',
          type: 'article',
          status: 'published',
          views: 12580,
          likes: 456,
          comments: 78,
          featured: true,
          createTime: new Date('2024-01-10 14:30:00'),
        },
        {
          id: 2,
          title: 'JavaScript 异步编程最佳实践',
          type: 'article',
          status: 'draft',
          views: 0,
          likes: 0,
          comments: 0,
          featured: false,
          createTime: new Date('2024-01-15 10:20:00'),
        },
      ],

      // 数据分析
      analysisMetrics: [
        {
          key: 'totalViews',
          label: '总浏览量',
          value: 125680,
          change: 12.5,
          icon: 'el-icon-view',
          iconClass: 'primary',
        },
        {
          key: 'totalUsers',
          label: '总用户数',
          value: 8945,
          change: 8.2,
          icon: 'el-icon-user',
          iconClass: 'success',
        },
        {
          key: 'totalRevenue',
          label: '总收入',
          value: 56789,
          change: -3.1,
          icon: 'el-icon-money',
          iconClass: 'warning',
        },
        {
          key: 'conversionRate',
          label: '转化率',
          value: 12.5,
          change: 5.8,
          icon: 'el-icon-pie-chart',
          iconClass: 'info',
        },
      ],

      // 账单信息
      accountBalance: {
        available: 12580.5,
        frozen: 150.0,
        totalSpent: 25680.8,
        totalEarned: 38260.3,
      },

      billingHistory: [
        {
          id: 'B202401150001',
          type: 'recharge',
          amount: 1000,
          description: '账户充值',
          status: 'success',
          createTime: new Date('2024-01-15 10:30:00'),
        },
        {
          id: 'B202401140002',
          type: 'consume',
          amount: -50,
          description: 'API调用费用',
          status: 'success',
          createTime: new Date('2024-01-14 16:20:00'),
        },
        {
          id: 'B202401130003',
          type: 'withdraw',
          amount: -500,
          description: '提现申请',
          status: 'pending',
          createTime: new Date('2024-01-13 09:15:00'),
        },
      ],

      // 系统设置
      settings: {
        theme: 'light',
        language: 'zh-CN',
        compactMode: false,
        showSidebar: true,
        showBreadcrumb: true,
        emailNotification: {
          system: true,
          security: true,
          billing: true,
          marketing: false,
        },
        smsNotification: {
          login: true,
          security: true,
          important: true,
        },
        desktopNotification: true,
        soundNotification: false,
        privacy: {
          publicProfile: true,
          showOnlineStatus: true,
          allowSearch: true,
          collectUsageData: false,
          personalizedRecommendations: true,
        },
        advanced: {
          apiRateLimit: 100,
          apiTimeout: 5000,
          enableCache: true,
          cacheExpiry: 24,
          experimentalEditor: false,
          aiAssistant: true,
        },
      },

      // 表单数据
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      },

      apiForm: {
        name: '',
        permissions: [],
        expiry: '90',
        description: '',
      },

      paymentForm: {
        amount: null,
        method: 'alipay',
      },

      // 弹窗控制
      showPasswordDialog: false,
      showCreateApiDialog: false,
      showPaymentDialog: false,
      showNotifications: false,

      // 选项卡状态
      activeTab: 'basic',
      activeMessageTab: 'system',
      activeAnalysisTab: 'popular',
      activeSettingsTab: 'appearance',
      activeNotificationTab: 'all',

      // 搜索和过滤
      contentSearchKeyword: '',
      contentStatusFilter: 'all',
      contentTypeFilter: 'all',
      billTypeFilter: 'all',
      billDateRange: null,
      analysisDateRange: null,

      // 分页数据
      contentPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      billPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 加载状态
      loadingContent: false,
      loadingBills: false,
      loadingPayment: false,

      // 选中项
      selectedContent: [],

      // 充值金额选项
      rechargeAmounts: [50, 100, 200, 500, 1000, 2000],

      // 其他数据
      popularContent: [
        {
          title: 'Vue.js 3.0 深度解析',
          views: 12580,
          likes: 456,
          comments: 78,
          shares: 23,
        },
      ],

      userBehaviorData: [
        {
          action: '页面浏览',
          count: 15680,
          percentage: 65.2,
          avgDuration: 180000,
        },
        {
          action: '内容互动',
          count: 3456,
          percentage: 14.3,
          avgDuration: 45000,
        },
      ],

      // 表单验证规则
      passwordRules: {
        currentPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' },
        ],
      },

      apiRules: {
        name: [{ required: true, message: '请输入密钥名称', trigger: 'blur' }],
        permissions: [{ required: true, message: '请选择权限范围', trigger: 'change' }],
      },
    };
  },

  computed: {
    // 计算属性
    unreadCount() {
      return (
        this.systemMessages.filter((msg) => !msg.read).length +
        this.interactionMessages.filter((msg) => !msg.read).length
      );
    },

    unreadSystemCount() {
      return this.systemMessages.filter((msg) => !msg.read).length;
    },

    unreadInteractionCount() {
      return this.interactionMessages.filter((msg) => !msg.read).length;
    },

    allNotifications() {
      return [...this.systemMessages, ...this.interactionMessages].sort(
        (a, b) => new Date(b.time) - new Date(a.time)
      );
    },

    systemNotifications() {
      return this.systemMessages.sort((a, b) => new Date(b.time) - new Date(a.time));
    },

    filteredContent() {
      let content = [...this.userContent];

      // 搜索过滤
      if (this.contentSearchKeyword) {
        content = content.filter((item) =>
          item.title.toLowerCase().includes(this.contentSearchKeyword.toLowerCase())
        );
      }

      // 状态过滤
      if (this.contentStatusFilter !== 'all') {
        content = content.filter((item) => item.status === this.contentStatusFilter);
      }

      // 类型过滤
      if (this.contentTypeFilter !== 'all') {
        content = content.filter((item) => item.type === this.contentTypeFilter);
      }

      return content;
    },

    filteredBills() {
      let bills = [...this.billingHistory];

      // 类型过滤
      if (this.billTypeFilter !== 'all') {
        bills = bills.filter((bill) => bill.type === this.billTypeFilter);
      }

      // 日期过滤
      if (this.billDateRange && this.billDateRange.length === 2) {
        const [startDate, endDate] = this.billDateRange;
        bills = bills.filter((bill) => {
          const billDate = new Date(bill.createTime);
          return billDate >= startDate && billDate <= endDate;
        });
      }

      return bills;
    },
  },

  mounted() {
    this.initUserInfo();
    this.initCharts();
    this.loadData();
  },

  methods: {
    // 初始化方法
    initUserInfo() {
      this.editForm = {
        nickname: this.userInfo.nickname,
        email: this.userInfo.email,
        phone: this.userInfo.phone,
        gender: this.userInfo.gender,
        birthday: this.userInfo.birthday,
        location: this.userInfo.location,
        bio: this.userInfo.bio,
        website: this.userInfo.website,
        company: this.userInfo.company,
        position: this.userInfo.position,
      };
    },

    initCharts() {
      this.$nextTick(() => {
        this.initOverviewChart();
        this.initApiUsageChart();
        this.initUserGrowthChart();
        this.initInteractionChart();
        this.initTrafficSourceChart();
      });
    },

    loadData() {
      this.contentPagination.total = this.userContent.length;
      this.billPagination.total = this.billingHistory.length;
    },

    // 图表初始化
    initOverviewChart() {
      if (!this.$refs.overviewChart) return;

      const chart = echarts.init(this.$refs.overviewChart);
      const option = {
        title: {
          text: '数据概览',
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
              { value: this.statsData.totalViews, name: '浏览量' },
              { value: this.statsData.totalLikes, name: '点赞数' },
              { value: this.statsData.totalComments, name: '评论数' },
              { value: this.statsData.followers, name: '粉丝数' },
            ],
          },
        ],
      };
      chart.setOption(option);
    },

    initApiUsageChart() {
      if (!this.$refs.apiUsageChart) return;

      const chart = echarts.init(this.$refs.apiUsageChart);
      const option = {
        title: {
          text: 'API调用统计',
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            type: 'line',
            data: [1200, 1350, 1100, 1450, 1600, 1380],
            smooth: true,
          },
        ],
      };
      chart.setOption(option);
    },

    initUserGrowthChart() {
      if (!this.$refs.userGrowthChart) return;

      const chart = echarts.init(this.$refs.userGrowthChart);
      const option = {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '新增用户',
            type: 'bar',
            data: [320, 450, 380, 520, 600, 480],
          },
        ],
      };
      chart.setOption(option);
    },

    initInteractionChart() {
      if (!this.$refs.interactionChart) return;

      const chart = echarts.init(this.$refs.interactionChart);
      const option = {
        tooltip: {
          trigger: 'item',
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [
              { value: 450, name: '点赞' },
              { value: 320, name: '评论' },
              { value: 180, name: '分享' },
              { value: 260, name: '收藏' },
            ],
          },
        ],
      };
      chart.setOption(option);
    },

    initTrafficSourceChart() {
      if (!this.$refs.trafficSourceChart) return;

      const chart = echarts.init(this.$refs.trafficSourceChart);
      const option = {
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: ['直接访问', '搜索引擎', '社交媒体', '推荐链接'],
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '直接访问',
            type: 'line',
            data: [1200, 1320, 1010, 1340, 1290, 1330],
          },
          {
            name: '搜索引擎',
            type: 'line',
            data: [820, 932, 901, 934, 1290, 1330],
          },
          {
            name: '社交媒体',
            type: 'line',
            data: [450, 520, 480, 610, 580, 620],
          },
          {
            name: '推荐链接',
            type: 'line',
            data: [320, 380, 350, 420, 450, 480],
          },
        ],
      };
      chart.setOption(option);
    },

    // 工具方法
    formatNumber(num, decimals = 0) {
      if (num === null || num === undefined) return '0';

      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
      }

      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
      });
    },

    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return (
        date.getFullYear() +
        '-' +
        String(date.getMonth() + 1).padStart(2, '0') +
        '-' +
        String(date.getDate()).padStart(2, '0') +
        ' ' +
        String(date.getHours()).padStart(2, '0') +
        ':' +
        String(date.getMinutes()).padStart(2, '0')
      );
    },

    formatRelativeTime(time) {
      if (!time) return '';

      const now = new Date();
      const diff = now - new Date(time);
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (minutes < 1) return '刚刚';
      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      if (days < 7) return `${days}天前`;
      return this.formatTime(time);
    },

    formatDuration(ms) {
      const seconds = Math.floor(ms / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`;
      } else {
        return `${seconds}秒`;
      }
    },

    // 获取样式类型
    getUserRoleType(role) {
      const roleTypes = {
        普通用户: '',
        高级用户: 'warning',
        VIP用户: 'success',
        管理员: 'danger',
      };
      return roleTypes[role] || '';
    },

    getMessageTypeClass(type) {
      return (
        {
          info: 'message-info',
          warning: 'message-warning',
          error: 'message-error',
          success: 'message-success',
        }[type] || 'message-info'
      );
    },

    getMessageIcon(type) {
      return (
        {
          info: 'el-icon-info',
          warning: 'el-icon-warning',
          error: 'el-icon-error',
          success: 'el-icon-success',
        }[type] || 'el-icon-info'
      );
    },

    getContentTypeTag(type) {
      return (
        {
          article: 'primary',
          video: 'success',
          image: 'warning',
        }[type] || 'info'
      );
    },

    getContentTypeText(type) {
      return (
        {
          article: '文章',
          video: '视频',
          image: '图片',
        }[type] || type
      );
    },

    getContentStatusTag(status) {
      return (
        {
          published: 'success',
          draft: 'info',
          pending: 'warning',
        }[status] || 'info'
      );
    },

    getContentStatusText(status) {
      return (
        {
          published: '已发布',
          draft: '草稿',
          pending: '审核中',
        }[status] || status
      );
    },

    getBillTypeTag(type) {
      return (
        {
          recharge: 'success',
          consume: 'warning',
          withdraw: 'info',
          refund: 'primary',
        }[type] || 'info'
      );
    },

    getBillTypeText(type) {
      return (
        {
          recharge: '充值',
          consume: '消费',
          withdraw: '提现',
          refund: '退款',
        }[type] || type
      );
    },

    getBillStatusTag(status) {
      return (
        {
          success: 'success',
          pending: 'warning',
          failed: 'danger',
        }[status] || 'info'
      );
    },

    getBillStatusText(status) {
      return (
        {
          success: '成功',
          pending: '处理中',
          failed: '失败',
        }[status] || status
      );
    },

    // 表单验证
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },

    // 事件处理方法
    handleCommand(command) {
      switch (command) {
        case 'export':
          this.$message.success('数据导出功能开发中...');
          break;
        case 'backup':
          this.$message.success('资料备份功能开发中...');
          break;
        case 'print':
          window.print();
          break;
        case 'help':
          window.open('/help', '_blank');
          break;
      }
    },

    refreshData() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.$message.success('数据刷新成功');
      }, 1000);
    },

    // 头像上传
    handleAvatarSuccess(res, file) {
      this.userInfo.avatar = URL.createObjectURL(file.raw);
      this.$message.success('头像上传成功');
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('头像图片只能是 JPG 或 PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },

    // 用户信息管理
    handleEdit() {
      this.$message.info('编辑用户信息');
    },

    saveBasicInfo() {
      this.userInfo = { ...this.userInfo, ...this.editForm };
      this.$message.success('基本信息保存成功');
    },

    resetBasicInfo() {
      this.initUserInfo();
      this.$message.info('已重置为原始信息');
    },

    changePassword() {
      this.showPasswordDialog = true;
    },

    submitPasswordChange() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          this.showPasswordDialog = false;
          this.passwordForm = {
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          };
          this.$message.success('密码修改成功');
        }
      });
    },

    changePhone() {
      this.$message.info('修改手机号功能开发中...');
    },

    enableTwoFactor() {
      this.$message.info('启用双重认证功能开发中...');
    },

    // API管理
    createApiKey() {
      this.showCreateApiDialog = true;
    },

    submitCreateApi() {
      this.$refs.apiForm.validate((valid) => {
        if (valid) {
          const newApiKey = {
            id: this.apiKeys.length + 1,
            name: this.apiForm.name,
            key: 'ak_' + Math.random().toString(36).substring(7),
            permissions: [...this.apiForm.permissions],
            status: 'active',
            createTime: new Date(),
            lastUsed: null,
            usageCount: 0,
          };
          this.apiKeys.push(newApiKey);
          this.showCreateApiDialog = false;
          this.apiForm = {
            name: '',
            permissions: [],
            expiry: '90',
            description: '',
          };
          this.$message.success('API密钥创建成功');
        }
      });
    },

    editApiKey(apiKey) {
      this.$message.info(`编辑API密钥: ${apiKey.name}`);
    },

    toggleApiKey(apiKey) {
      apiKey.status = apiKey.status === 'active' ? 'inactive' : 'active';
      this.$message.success(`API密钥已${apiKey.status === 'active' ? '启用' : '禁用'}`);
    },

    deleteApiKey(apiKey) {
      this.$confirm('确定要删除此API密钥吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.apiKeys.indexOf(apiKey);
          if (index > -1) {
            this.apiKeys.splice(index, 1);
            this.$message.success('API密钥删除成功');
          }
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    copyApiKey(key) {
      navigator.clipboard
        .writeText(key)
        .then(() => {
          this.$message.success('API密钥已复制到剪贴板');
        })
        .catch(() => {
          this.$message.error('复制失败，请手动复制');
        });
    },

    // 消息管理
    markAsRead(messageId, type) {
      const messages = type === 'system' ? this.systemMessages : this.interactionMessages;
      const message = messages.find((msg) => msg.id === messageId);
      if (message) {
        message.read = true;
        this.$message.success('消息已标记为已读');
      }
    },

    markAllAsRead() {
      this.systemMessages.forEach((msg) => (msg.read = true));
      this.interactionMessages.forEach((msg) => (msg.read = true));
      this.$message.success('所有消息已标记为已读');
    },

    deleteMessage(messageId, type) {
      const messages = type === 'system' ? this.systemMessages : this.interactionMessages;
      const index = messages.findIndex((msg) => msg.id === messageId);
      if (index > -1) {
        messages.splice(index, 1);
        this.$message.success('消息删除成功');
      }
    },

    clearAllNotifications() {
      this.$confirm('确定要清空所有通知吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.systemMessages = [];
        this.interactionMessages = [];
        this.$message.success('所有通知已清空');
      });
    },

    replyMessage(message) {
      this.$message.info(`回复消息: ${message.title}`);
    },

    handleNotificationClick(notification) {
      if (!notification.read) {
        notification.read = true;
      }
      this.$message.info(`查看通知: ${notification.title}`);
    },

    // 内容管理
    createContent() {
      this.$message.info('创建内容功能开发中...');
    },

    editContent(content) {
      this.$message.info(`编辑内容: ${content.title}`);
    },

    deleteContent(content) {
      this.$confirm('确定要删除此内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const index = this.userContent.indexOf(content);
        if (index > -1) {
          this.userContent.splice(index, 1);
          this.$message.success('内容删除成功');
        }
      });
    },

    toggleFeatured(content) {
      content.featured = !content.featured;
      this.$message.success(`内容${content.featured ? '已设为精选' : '已取消精选'}`);
    },

    batchDeleteContent() {
      if (this.selectedContent.length === 0) {
        this.$message.warning('请选择要删除的内容');
        return;
      }

      this.$confirm(`确定要删除选中的${this.selectedContent.length}个内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.selectedContent.forEach((content) => {
          const index = this.userContent.indexOf(content);
          if (index > -1) {
            this.userContent.splice(index, 1);
          }
        });
        this.selectedContent = [];
        this.$message.success('批量删除成功');
      });
    },

    handleContentSelectionChange(selection) {
      this.selectedContent = selection;
    },

    handleContentPageChange(page) {
      this.contentPagination.currentPage = page;
      this.loadContentData();
    },

    loadContentData() {
      this.loadingContent = true;
      setTimeout(() => {
        this.loadingContent = false;
      }, 500);
    },

    // 财务管理
    showRecharge() {
      this.showPaymentDialog = true;
    },

    submitPayment() {
      if (!this.paymentForm.amount || this.paymentForm.amount <= 0) {
        this.$message.error('请输入有效的充值金额');
        return;
      }

      this.loadingPayment = true;
      setTimeout(() => {
        this.accountBalance.available += this.paymentForm.amount;
        this.billingHistory.unshift({
          id: 'B' + Date.now(),
          type: 'recharge',
          amount: this.paymentForm.amount,
          description: '账户充值',
          status: 'success',
          createTime: new Date(),
        });
        this.loadingPayment = false;
        this.showPaymentDialog = false;
        this.paymentForm = {
          amount: null,
          method: 'alipay',
        };
        this.$message.success('充值成功');
      }, 2000);
    },

    applyWithdraw() {
      this.$message.info('申请提现功能开发中...');
    },

    downloadBill() {
      this.$message.info('下载账单功能开发中...');
    },

    exportBills() {
      this.$message.info('导出账单功能开发中...');
    },

    handleBillPageChange(page) {
      this.billPagination.currentPage = page;
      this.loadBillData();
    },

    loadBillData() {
      this.loadingBills = true;
      setTimeout(() => {
        this.loadingBills = false;
      }, 500);
    },

    // 设置管理
    saveSettings() {
      this.$message.success('设置已保存');
    },

    resetSettings() {
      this.settings = {
        theme: 'light',
        language: 'zh-CN',
        compactMode: false,
        showSidebar: true,
        showBreadcrumb: true,
        emailNotification: {
          system: true,
          security: true,
          billing: true,
          marketing: false,
        },
        smsNotification: {
          login: true,
          security: true,
          important: true,
        },
        desktopNotification: true,
        soundNotification: false,
        privacy: {
          publicProfile: true,
          showOnlineStatus: true,
          allowSearch: true,
          collectUsageData: false,
          personalizedRecommendations: true,
        },
        advanced: {
          apiRateLimit: 100,
          apiTimeout: 5000,
          enableCache: true,
          cacheExpiry: 24,
          experimentalEditor: false,
          aiAssistant: true,
        },
      };
      this.$message.success('设置已重置');
    },

    // 搜索和过滤
    handleContentSearch() {
      this.contentPagination.currentPage = 1;
      this.loadContentData();
    },

    resetContentFilters() {
      this.contentSearchKeyword = '';
      this.contentStatusFilter = 'all';
      this.contentTypeFilter = 'all';
      this.contentPagination.currentPage = 1;
      this.loadContentData();
    },

    handleBillSearch() {
      this.billPagination.currentPage = 1;
      this.loadBillData();
    },

    resetBillFilters() {
      this.billTypeFilter = 'all';
      this.billDateRange = null;
      this.billPagination.currentPage = 1;
      this.loadBillData();
    },
  },
};
</script>

<style scoped lang="scss">
.user-center {
  min-height: 100vh;
  background: #f5f7fa;

  &.dark-theme {
    background: #18243c;
    color: #ffffff;

    .top-navbar {
      background: #1f2937;
      border-color: #374151;
    }

    .sidebar-menu {
      background: #1f2937;
      border-color: #374151;
    }

    .content-area {
      background: #1f2937;
    }
  }
}

.top-navbar {
  background: #ffffff;
  border-bottom: 1px solid #e6e8eb;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;

  .navbar-content {
    max-width: 1200px;
    margin: 0 auto;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .nav-left {
      display: flex;
      align-items: center;
      gap: 20px;

      h2 {
        margin: 0;
        font-size: 18px;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #3b82f6;
        }
      }
    }

    .nav-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .notification-badge {
        margin-right: 8px;
      }
    }
  }
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  gap: 20px;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 10px;
  }
}

.sidebar-menu {
  width: 250px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: fit-content;
  position: sticky;
  top: 84px;

  @media (max-width: 768px) {
    width: 100%;
    position: static;
  }

  .user-avatar-section {
    padding: 20px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .user-avatar-container {
      position: relative;
      display: inline-block;
      margin-bottom: 12px;

      .online-status {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
        height: 16px;
        background: #10b981;
        border: 2px solid white;
        border-radius: 50%;

        &.offline {
          background: #9ca3af;
        }
      }
    }

    .user-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .user-role {
      font-size: 12px;
      opacity: 0.9;
    }
  }

  .menu-list {
    padding: 0;
    margin: 0;
    list-style: none;

    .menu-item {
      .menu-link {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;

        &:hover {
          background: #f9fafb;
          color: #3b82f6;
        }

        &.active {
          background: #eff6ff;
          color: #3b82f6;
          border-left-color: #3b82f6;
        }

        i {
          width: 20px;
          margin-right: 12px;
          text-align: center;
        }

        .menu-text {
          flex: 1;
        }

        .badge {
          background: #ef4444;
          color: white;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 10px;
          min-width: 16px;
          text-align: center;
        }
      }
    }
  }
}

.content-area {
  flex: 1;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 数据总览样式
.overview-section {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      padding: 20px;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;

      &.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.success {
        background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
      }

      &.warning {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }

      &.danger {
        background: linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%);
      }

      .stat-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .stat-value {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;

    .chart-container {
      min-height: 300px;

      .chart {
        width: 100%;
        height: 300px;
      }
    }
  }
}

// 基本信息样式
.profile-section {
  .profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;

    .profile-avatar {
      .avatar-upload {
        .el-upload {
          border: 2px dashed rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          cursor: pointer;
          position: relative;
          overflow: hidden;

          &:hover {
            border-color: rgba(255, 255, 255, 0.6);
          }
        }

        .avatar-uploader-icon {
          font-size: 28px;
          color: rgba(255, 255, 255, 0.8);
          width: 80px;
          height: 80px;
          line-height: 80px;
          text-align: center;
        }
      }
    }

    .profile-info {
      flex: 1;

      .profile-name {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .profile-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        opacity: 0.9;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            width: 16px;
          }
        }
      }
    }
  }

  .profile-tabs {
    .el-tabs__header {
      margin-bottom: 20px;
    }

    .form-section {
      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
      }
    }
  }
}

// 账号安全样式
.security-section {
  .security-grid {
    display: grid;
    gap: 16px;

    .security-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;

      .security-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .security-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f3f4f6;
          color: #6b7280;

          &.success {
            background: #dcfce7;
            color: #16a34a;
          }

          &.warning {
            background: #fef3c7;
            color: #d97706;
          }
        }

        .security-text {
          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            color: #1f2937;
          }

          p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }
  }
}

// API管理样式
.api-section {
  .api-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .api-table {
    .api-key-cell {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #6b7280;
    }

    .api-permissions {
      .permission-tag {
        margin-right: 4px;
      }
    }
  }
}

// 活动记录样式
.activity-section {
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      .activity-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;

        &.success {
          background: #dcfce7;
          color: #16a34a;
        }

        &.failed {
          background: #fee2e2;
          color: #dc2626;
        }
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-size: 14px;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .activity-details {
          font-size: 12px;
          color: #6b7280;

          .activity-meta {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
          }
        }
      }

      .activity-time {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }
}

// 消息中心样式
.message-section {
  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .message-list {
    .message-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;

      &.unread {
        background: #eff6ff;
        border-left: 3px solid #3b82f6;
        padding-left: 12px;
      }

      &:last-child {
        border-bottom: none;
      }

      .message-content {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          color: #1f2937;
        }

        p {
          margin: 0 0 8px 0;
          font-size: 12px;
          color: #6b7280;
          line-height: 1.5;
        }

        .message-time {
          font-size: 11px;
          color: #9ca3af;
        }
      }

      .message-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .message-actions {
        opacity: 1;
      }
    }
  }

  .announcement-list {
    .announcement-item {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .announcement-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #1f2937;
        }
      }

      .announcement-content {
        margin-bottom: 12px;
        color: #6b7280;
        line-height: 1.6;
      }

      .announcement-footer {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #9ca3af;

        @media (max-width: 480px) {
          flex-direction: column;
          gap: 4px;
        }
      }
    }
  }
}

// 内容管理样式
.content-section {
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
  }

  .content-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .content-table {
    .content-title {
      font-weight: 500;
      color: #1f2937;
    }

    .content-meta {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #6b7280;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}

// 数据分析样式
.analysis-section {
  .analysis-tabs {
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .metric-card {
        padding: 20px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        text-align: center;

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 12px;
          font-size: 20px;

          &.primary {
            background: #dbeafe;
            color: #3b82f6;
          }

          &.success {
            background: #dcfce7;
            color: #16a34a;
          }

          &.warning {
            background: #fef3c7;
            color: #d97706;
          }

          &.info {
            background: #e0f2fe;
            color: #0891b2;
          }
        }

        .metric-value {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .metric-label {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .metric-change {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 12px;

          &.positive {
            background: #dcfce7;
            color: #16a34a;
          }

          &.negative {
            background: #fee2e2;
            color: #dc2626;
          }
        }
      }
    }
  }
}

// 财务管理样式
.billing-section {
  .balance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .balance-card {
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      color: white;

      &.available {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.frozen {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }

      &.spent {
        background: linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%);
      }

      &.earned {
        background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
      }

      .balance-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .balance-amount {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 4px;
      }

      .balance-label {
        font-size: 12px;
        opacity: 0.9;
      }
    }
  }

  .billing-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .billing-table {
    .amount-cell {
      font-weight: 600;

      &.positive {
        color: #16a34a;
      }

      &.negative {
        color: #dc2626;
      }
    }

    .status-cell {
      .status-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;

        &.success {
          background: #16a34a;
        }

        &.pending {
          background: #d97706;
        }

        &.failed {
          background: #dc2626;
        }
      }
    }
  }
}

// 通知设置样式
.notifications-section {
  .notification-groups {
    .notification-group {
      margin-bottom: 24px;

      .group-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
      }

      .notification-items {
        display: grid;
        gap: 12px;

        .notification-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;

          .notification-info {
            .notification-title {
              font-size: 14px;
              color: #1f2937;
              margin-bottom: 4px;
            }

            .notification-desc {
              font-size: 12px;
              color: #6b7280;
            }
          }
        }
      }
    }
  }
}

// 系统设置样式
.settings-section {
  .settings-groups {
    .settings-group {
      margin-bottom: 32px;

      .group-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #3b82f6;
        }
      }

      .settings-grid {
        display: grid;
        gap: 16px;

        .setting-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;

          .setting-info {
            flex: 1;

            .setting-title {
              font-size: 14px;
              color: #1f2937;
              margin-bottom: 4px;
            }

            .setting-desc {
              font-size: 12px;
              color: #6b7280;
              line-height: 1.4;
            }
          }

          .setting-control {
            margin-left: 16px;
          }
        }
      }
    }
  }

  .settings-actions {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
  }
}

// 弹窗样式
.payment-dialog {
  .amount-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #1f2937;
    }

    .amount-options {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 12px;

      .amount-option {
        padding: 12px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
        }

        &.active {
          border-color: #3b82f6;
          background: #eff6ff;
          color: #3b82f6;
        }
      }
    }

    .custom-amount {
      .el-input-group__prepend {
        background: #f9fafb;
      }
    }
  }

  .payment-method {
    h4 {
      margin: 0 0 12px 0;
      color: #1f2937;
    }

    .el-radio {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .payment-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }
  }
}

.notification-drawer {
  .notification-header {
    display: flex;
    justify-content: space-between;
    padding: 0 0 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .notification-list {
    .notification-item {
      padding: 12px 0;
      border-bottom: 1px solid #f3f4f6;
      cursor: pointer;
      transition: background 0.3s ease;

      &:hover {
        background: #f9fafb;
      }

      &.unread {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -16px;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #3b82f6;
        }
      }

      .notification-title {
        font-size: 14px;
        color: #1f2937;
        margin-bottom: 4px;
      }

      .notification-content {
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .notification-time {
        font-size: 11px;
        color: #9ca3af;
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .main-container {
    padding: 10px;
  }

  .sidebar-menu {
    width: 100%;
    position: static;
    margin-bottom: 20px;
  }

  .stats-grid,
  .balance-cards,
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-filters,
  .billing-filters {
    flex-direction: column;
  }

  .content-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .navbar-content {
    .nav-left {
      h2 {
        font-size: 16px;
      }
    }

    .nav-right {
      gap: 8px;
    }
  }
}

@media (max-width: 480px) {
  .stats-grid,
  .balance-cards,
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .amount-options {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;

    .profile-meta {
      justify-content: center;
    }
  }

  .message-item {
    flex-direction: column;
    gap: 8px !important;

    .message-actions {
      opacity: 1 !important;
      align-self: flex-end;
    }
  }
}

// 加载状态样式
.loading {
  opacity: 0.6;
  pointer-events: none;
}

// 动画样式
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(-100%);
}

// 滚动条样式
.content-area {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// 打印样式
@media print {
  .sidebar-menu,
  .top-navbar,
  .el-button,
  .el-pagination {
    display: none !important;
  }

  .main-container {
    padding: 0;
    max-width: none;
  }

  .content-area {
    box-shadow: none;
    border: none;
  }
}

// 自定义Element UI样式覆盖
.el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: #f9fafb;
        color: #374151;
        font-weight: 600;
      }
    }
  }
}

.el-card {
  border: 1px solid #e5e7eb;

  .el-card__header {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
}

.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;

    .el-tabs__nav-wrap {
      &::after {
        background: #e5e7eb;
      }
    }

    .el-tabs__item {
      &.is-active {
        color: #3b82f6;
      }
    }

    .el-tabs__active-bar {
      background: #3b82f6;
    }
  }
}

.el-form {
  .el-form-item__label {
    color: #374151;
    font-weight: 500;
  }
}

.el-dialog {
  .el-dialog__header {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;

    .el-dialog__title {
      color: #1f2937;
      font-weight: 600;
    }
  }
}

.el-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    .el-drawer__title {
      color: #1f2937;
      font-weight: 600;
    }
  }
}

// 自定义Badge样式
.custom-badge {
  .el-badge__content {
    background: #3b82f6;
    border-color: #3b82f6;
  }
}

// 工具提示样式
.el-tooltip__popper {
  background: #1f2937;
  color: white;
  border-color: #1f2937;

  .popper__arrow {
    border-top-color: #1f2937;
    border-bottom-color: #1f2937;
  }
}
</style>
